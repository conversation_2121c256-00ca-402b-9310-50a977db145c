import * as process from 'process'

import { z } from 'zod'

export const envConfigSchema = z.object({
  env: z.enum(['dev', 'prod'] as const),
  port: z.number(),

  // Admin access
  adminKey: z.string().optional(),
  adminNotificationEmail: z.string().optional(),
  adminPanelUrl: z.string().optional(),

  // Template paths
  templatePath: z.string().optional(),

  // Database
  database: z.object({
    url: z.string().min(1),
  }),

  // AWS S3 for image uploads
  aws: z.object({
    accessKeyId: z.string().min(1),
    secretAccessKey: z.string().min(1),
    region: z.string().min(1),
    s3BucketName: z.string().min(1),
  }),

  // SMS
  twilio: z.object({
    accountSid: z.string().min(1),
    authToken: z.string().min(1),
    verifyServiceSid: z.string().min(1),
  }),

  firebase: z.object({
    projectId: z.string().min(1),
    privateKey: z.string().min(1),
    clientEmail: z.string().min(1),
  }),

  notifications: z.object({
    notifyEventApiKey: z.string().min(1),
  }),

  telegram: z.object({
    apiToken: z.string().min(1),
    chatId: z.string().min(1),
  }),

  yellowCard: z.object({
    name: z.string().min(1),
    secretKey: z.string().min(1),
    apiKey: z.string().min(1),
  }),

  moonPay: z.object({
    name: z.string().min(1),
    secretKey: z.string().min(1),
    publicKey: z.string().min(1),
  }),

  indexer: z.object({
    apiUrl: z.string().min(1),
  }),

  explorer: z.object({
    intentLinkTemplate: z.string().min(1),
    templatePlaceholder: z.string().min(1),
  }),

  l3Explorer: z.object({
    txLinkTemplate: z.string().min(1),
    templatePlaceholder: z.string().min(1),
  }),

  mailCoach: z.object({
    apiUrl: z.string().min(1),
    apiKey: z.string().min(1),
    senderName: z.string().min(1),
    senderEmail: z.string().min(1),
  }),
  mailCoachAuth: z.object({
    senderName: z.string().min(1),
    senderEmail: z.string().min(1),
  }),

  coinMarketCap: z.object({
    apiUrl: z.string().min(1),
    apiKey: z.string().min(1),
  }),

  lunarCrush: z.object({
    apiKey: z.string().min(1),
    twitterIds: z.array(z.string().min(1)),
  }),

  coinDesk: z.object({
    apiKey: z.string().min(1),
    newsSources: z.array(z.string().min(1)),
  }),

  cashRamp: z.object({
    secretKey: z.string().min(1),
    publicKey: z.string().min(1),
  }),

  onrampMoney: z.object({
    appId: z.string().min(1),
    apiKey: z.string().min(1),
    secretKey: z.string().min(1),
  }),

  wallets: z.object({
    sweep: z.object({
      evmPrivateKey: z.string().min(1),
      evmAddress: z.string().min(1),
      solanaPrivateKey: z.string().min(1),
      solanaAddress: z.string().min(1),
      expectedUsdcAmount: z.number().positive(),
    }),
    poolManager: z.object({
      evmPrivateKey: z.string().min(1),
      solanaPrivateKey: z.string().min(1),
    }),
  }),

  crypto: z.object({
    secretKey: z.string().min(32),
  }),

  blockchain: z.object({
    solanaRpcUrl: z.string().min(1),
    ethereumRpcUrl: z.string().min(1),
    arbitrumRpcUrl: z.string().min(1),
    baseRpcUrl: z.string().min(1),
  }),
})

type EnvConfig = z.infer<typeof envConfigSchema>

export const getEnvConfig = (): EnvConfig => ({
  env: process.env.NODE_ENV === 'production' ? 'prod' : 'dev',
  port: Number(process.env.PORT || 3001),

  // Admin access
  adminKey: process.env.FORM_ADMIN_KEY ? String(process.env.FORM_ADMIN_KEY).trim() : undefined,
  adminNotificationEmail: process.env.ADMIN_NOTIFICATION_EMAIL
    ? String(process.env.ADMIN_NOTIFICATION_EMAIL).trim()
    : undefined,
  adminPanelUrl: process.env.ADMIN_PANEL_URL ? String(process.env.ADMIN_PANEL_URL).trim() : undefined,

  // Template paths
  templatePath: process.env.TEMPLATE_PATH ? String(process.env.TEMPLATE_PATH).trim() : undefined,

  database: {
    url: String(process.env.DATABASE_URL || '').trim(),
  },

  // AWS S3 for image uploads
  aws: {
    accessKeyId: String(process.env.IL_SERVICE_AWS_ACCESS_KEY_ID || '').trim(),
    secretAccessKey: String(process.env.IL_SERVICE_AWS_SECRET_ACCESS_KEY || '').trim(),
    region: String(process.env.IL_SERVICE_AWS_REGION || '').trim(),
    s3BucketName: String(process.env.IL_SERVICE_AWS_S3_BUCKET_NAME || '').trim(),
  },

  twilio: {
    accountSid: String(process.env.TWILIO_ACCOUNT_SID || '').trim(),
    authToken: String(process.env.TWILIO_AUTH_TOKEN || '').trim(),
    verifyServiceSid: String(process.env.TWILIO_VERIFY_SERVICE_SID || '').trim(),
  },

  firebase: {
    projectId: String(process.env.FB_ADMIN_PROJECT_ID || '').trim(),
    privateKey: JSON.parse(process.env.FB_ADMIN_PRIVATE_KEY || '{}').privateKey,
    clientEmail: String(process.env.FB_ADMIN_CLIENT_EMAIL || '').trim(),
  },

  notifications: {
    notifyEventApiKey: String(process.env.NOTIFY_EVENT_API_KEY || '').trim(),
  },

  telegram: {
    apiToken: String(process.env.TG_BOT_API_TOKEN || '').trim(),
    chatId: String(process.env.TG_INTENT_STATUS_CHAT_ID || '').trim(),
  },

  yellowCard: {
    name: String(process.env.YELLOW_CARD_PROVIDER_NAME || '').trim(),
    secretKey: String(process.env.YELLOW_CARD_SECRET_KEY || '').trim(),
    apiKey: String(process.env.YELLOW_CARD_API_KEY || '').trim(),
  },

  moonPay: {
    name: String(process.env.MOONPAY_PROVIDER_NAME || '').trim(),
    secretKey: String(process.env.MOONPAY_SECRET_KEY || '').trim(),
    publicKey: String(process.env.MOONPAY_PUBLIC_KEY || '').trim(),
  },

  indexer: {
    apiUrl: String(process.env.INDEXER_API_URL || '').trim(),
  },

  explorer: {
    intentLinkTemplate: String(process.env.EXPLORER_INTENT_LINK_TEMPLATE || '').trim(),
    templatePlaceholder: String(process.env.EXPLORER_TEMPLATE_PLACEHOLDER || '').trim(),
  },

  l3Explorer: {
    txLinkTemplate: String(process.env.L3_TX_LINK_TEMPLATE || '').trim(),
    templatePlaceholder: String(process.env.L3_TX_TEMPLATE_PLACEHOLDER || '').trim(),
  },

  mailCoach: {
    apiUrl: String(process.env.MAILCOACH_API_URL || '').trim(),
    apiKey: String(process.env.MAILCOACH_API_KEY || '').trim(),
    senderName: String(process.env.MAILCOACH_SENDER_NAME || '').trim(),
    senderEmail: String(process.env.MAILCOACH_SENDER_EMAIL || '').trim(),
  },
  mailCoachAuth: {
    senderName: String(process.env.MAILCOACH_AUTH_SENDER_NAME || '').trim(),
    senderEmail: String(process.env.MAILCOACH_AUTH_SENDER_EMAIL || '').trim(),
  },

  coinMarketCap: {
    apiUrl: String(process.env.CMC_API_URL || '').trim(),
    apiKey: String(process.env.CMC_API_KEY || '').trim(),
  },

  lunarCrush: {
    apiKey: String(process.env.LUNAR_CRUSH_API_KEY || '').trim(),
    twitterIds: String(process.env.LUNAR_CRUSH_TWITTER_IDS || '')
      .trim()
      .split(','),
  },

  coinDesk: {
    apiKey: String(process.env.COINDESK_API_KEY || '').trim(),
    newsSources: String(process.env.COINDESK_NEWS_SOURCES || '')
      .trim()
      .split(','),
  },

  cashRamp: {
    secretKey: String(process.env.CASHRAMP_SECRET_KEY || '').trim(),
    publicKey: String(process.env.CASHRAMP_PUBLIC_KEY || '').trim(),
  },

  onrampMoney: {
    appId: String(process.env.ONRAMP_MONEY_APP_ID || '').trim(),
    apiKey: String(process.env.ONRAMP_MONEY_API_KEY || '').trim(),
    secretKey: String(process.env.ONRAMP_MONEY_SECRET_KEY || '').trim(),
  },

  wallets: {
    sweep: {
      evmPrivateKey: String(process.env.EVM_SWEEP_SIGNER_PRIVATE_KEY || '').trim(),
      evmAddress: String(process.env.SWEEP_EVM_ADDRESS || '').trim(),
      solanaPrivateKey: String(process.env.SOLANA_SWEEP_SIGNER_PRIVATE_KEY || '').trim(),
      solanaAddress: String(process.env.SWEEP_SOLANA_ADDRESS || '').trim(),
      expectedUsdcAmount: Number(process.env.EXPECTED_USDC_AMOUNT || '25000'),
    },
    poolManager: {
      evmPrivateKey: String(process.env.POOL_MANAGER_EVM_PRIVATE_KEY || '').trim(),
      solanaPrivateKey: String(process.env.POOL_MANAGER_SOLANA_PRIVATE_KEY || '').trim(),
    },
  },

  crypto: {
    secretKey: String(process.env.CRYPTO_SECRET_KEY || '').trim(),
  },

  blockchain: {
    solanaRpcUrl: String(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com').trim(),
    ethereumRpcUrl: String(process.env.ETHEREUM_RPC_URL || 'https://eth.drpc.org').trim(),
    arbitrumRpcUrl: String(process.env.ARBITRUM_RPC_URL || 'https://arbitrum.drpc.org').trim(),
    baseRpcUrl: String(process.env.BASE_RPC_URL || 'https://mainnet.base.org').trim(),
  },
})
