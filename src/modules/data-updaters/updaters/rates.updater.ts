import { Injectable, Logger } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { Cron } from '@nestjs/schedule'

import { type Rate, type TokenInfo, TokensService } from '@/modules/tokens'
import { keyBy } from '@/utils/arrays/key-by'
import { splitIntoChunks } from '@/utils/arrays/split-into-chunks'

import { CmcClient } from '../clients/cmc'
import { BATCH_SIZE, EventName } from '../data-updaters.constants'

@Injectable()
export class RatesUpdater {
  private logger = new Logger('RatesUpdater')

  constructor(
    private tokensService: TokensService,
    private cmcClient: CmcClient,
    private eventEmitter: EventEmitter2,
  ) {}

  // every 20 min
  @Cron('0 */20 * * * *', {
    waitForCompletion: true,
    name: 'cronUpdateRates',
    disabled: process.env.NODE_ENV !== 'production',
  })
  private async cronUpdateRates() {
    await this.updateAllRates(20 * 60 * 1000)
  }

  async updateAllRates(interval?: number): Promise<void> {
    const tokens = await this.tokensService.getTokens({})
    await this.fetchRates(tokens, interval)
    this.eventEmitter.emit(EventName.RatesUpdated)
  }

  private async fetchRates(tokens: TokenInfo[], updateInterval?: number): Promise<void> {
    let needsUpdate: Record<string, TokenInfo> = {}

    if (updateInterval) {
      for (const token of tokens) {
        const rate = await this.tokensService.getStoredRateById(token.id)

        if (!rate || !rate?.updatedAt || Date.now() - new Date(rate.updatedAt).getTime() >= updateInterval) {
          needsUpdate[token.cmcId] = token
        }
      }
    } else {
      needsUpdate = keyBy(tokens, (token) => token.cmcId)
    }

    const needsUpdateArr = Object.values(needsUpdate)
    const chunks = splitIntoChunks(needsUpdateArr, BATCH_SIZE)
    this.logger.log(
      `Update rates for ${needsUpdateArr.length} tokens. Chunks count: ${chunks.length}. Total tokens: ${tokens.length}`,
    )

    for (const chunk of chunks) {
      try {
        const cmcIds = chunk.map((token) => token.cmcId)
        const rates = await this.cmcClient.getHistoricalRates(cmcIds)
        this.logger.log(`Got rates for cmcIds: ${cmcIds.join(',')}`)

        const ratesToSave: Rate[] = []

        cmcIds.forEach((cmcId) => {
          try {
            const token = needsUpdate[cmcId]
            const rate = rates[cmcId]

            if (!token || !rate) {
              this.logger.warn(`Rate for token ${token.id} is missing. CMC id ${cmcId}`)

              return
            }

            ratesToSave.push({ ...rate, tokenId: token.id })
          } catch (e) {
            this.logger.error(e)
          }
        })

        if (ratesToSave.length > 0) {
          await this.tokensService.updateRates(ratesToSave)
        } else {
          this.logger.warn('No rates to save')
        }
      } catch (e) {
        this.logger.error(e)
      }
    }

    this.logger.log('Tokens rates update completed successfully')
  }
}
