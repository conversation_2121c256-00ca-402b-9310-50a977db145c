import { ApiProperty } from '@nestjs/swagger'
import { createZodDto } from 'nestjs-zod'
import { z } from 'zod'

const updateStatusSchema = z.object({
  status: z.enum([
    'draft',
    'submitted',
    'under_review',
    'needs_revision',
    'approved',
    'wallet_provided',
    'fee_paid',
    'liquidity_provided',
  ]),
  comment: z.string().optional(),
})

export class UpdateProjectStatusDto extends createZodDto(updateStatusSchema) {
  @ApiProperty({
    example: 'under_review',
    enum: [
      'draft',
      'submitted',
      'under_review',
      'needs_revision',
      'approved',
      'wallet_provided',
      'fee_paid',
      'liquidity_provided',
    ],
    description: 'New status for the project',
  })
  status!:
    | 'draft'
    | 'submitted'
    | 'under_review'
    | 'needs_revision'
    | 'approved'
    | 'wallet_provided'
    | 'fee_paid'
    | 'liquidity_provided'

  @ApiProperty({
    example: 'Needs more information about security audits',
    description: 'Optional comment for status change',
    required: false,
  })
  comment?: string
}
