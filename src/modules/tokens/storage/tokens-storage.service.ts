import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

import { Storage } from '@/abstract/storage'
import type { TokenToCreate } from '@/modules/tokens'
import { getErrorMessage } from '@/utils/get-error-message'
import { deleteUndefinedFields } from '@/utils/objects/delete-undefined-fields'

import { mapTokenInfo, type StoredRate, unmapTokenInfo } from './tokens-storage.mapper'
import {
  type MarketDataFromStorage,
  marketDataFromStorageSchema,
  rateFromStorageSchema,
  type TokenInfoFromStorage,
  tokenInfoFromStorageSchema,
} from './tokens-storage.schema'
import { FirebaseService } from '../../firebase'
import { type MarketData } from '../schemas/market-data.schema'
import { type Rate, rateSchema } from '../schemas/rate.schema'
import { type TokenInfo, tokenInfoSchema } from '../schemas/token-info.schema'

@Injectable()
export class TokensStorage extends Storage implements OnModuleInit {
  private logger = new Logger('TokensStorage')

  private tradableTokens: TokenInfo[] = []
  private tradableRates: StoredRate[] = []

  private nonTradableTokens: TokenInfo[] = []
  private nonTradableRates: StoredRate[] = []

  private tokensMap: Map<string, TokenInfo> = new Map<string, TokenInfo>()
  private ratesMap: Map<string, StoredRate> = new Map<string, StoredRate>()

  private disabledTokens: string[] = []

  constructor(private firebase: FirebaseService) {
    super()
  }

  onModuleInit() {
    void this.fetchTokens()
  }

  async revalidateCache(): Promise<void> {
    if (!this.isInitialized) return

    super.revalidateCache()
    this.tradableTokens = []
    this.tradableRates = []
    this.nonTradableTokens = []
    this.nonTradableRates = []
    this.tokensMap = new Map<string, TokenInfo>()
    this.ratesMap = new Map<string, StoredRate>()
    this.disabledTokens = []
    this.onModuleInit()
  }

  @Cron(CronExpression.EVERY_MINUTE, {
    waitForCompletion: true,
    name: 'updateStoredTokens',
  })
  private async updateStoredTokens(): Promise<void> {
    if (!this.isInitialized && !this.isInitializing) {
      this.logger.warn(`TokensStorage isn't initialized, initializing...`)
      await this.fetchTokens()
    }
  }

  async getTokens(params: { isTradable?: boolean }): Promise<TokenInfo[]> {
    if (!this.isInitialized) {
      await this.waitForInitPromise
    }

    if (typeof params.isTradable === 'boolean') {
      return params.isTradable ? this.tradableTokens : this.nonTradableTokens
    }

    return this.tradableTokens.concat(this.nonTradableTokens)
  }

  async getTokenById(id: string): Promise<TokenInfo | undefined> {
    if (!this.isInitialized) {
      await this.waitForInitPromise
    }

    return this.tokensMap.get(id)
  }

  async getRates(params: { isTradable?: boolean; includeHistory?: boolean }): Promise<Rate[]> {
    if (!this.isInitialized) {
      await this.waitForInitPromise
    }

    let rates: StoredRate[]

    if (typeof params.isTradable === 'boolean') {
      rates = params.isTradable ? this.tradableRates : this.nonTradableRates
    } else {
      rates = this.tradableRates.concat(this.nonTradableRates)
    }

    return rates.map((rate) => {
      const baseRate: Rate = { rate: rate.rate, percentChange: rate.percentChange, tokenId: rate.tokenId }

      return params.includeHistory ? { ...baseRate, rateHistory: rate.rateHistory } : baseRate
    })
  }

  async getRateById(id: string): Promise<Rate | undefined> {
    if (!this.isInitialized) {
      await this.waitForInitPromise
    }

    const storedRate = this.ratesMap.get(id)

    if (!storedRate) return undefined

    return {
      rate: storedRate.rate,
      percentChange: storedRate.percentChange,
      rateHistory: storedRate.rateHistory,
      tokenId: storedRate.tokenId,
    }
  }

  async getStoredRateById(id: string): Promise<StoredRate | undefined> {
    if (!this.isInitialized) {
      await this.waitForInitPromise
    }

    return this.ratesMap.get(id)
  }

  async getDisabledTokens(): Promise<string[]> {
    if (!this.isInitialized) {
      await this.waitForInitPromise
    }

    return this.disabledTokens
  }

  async updateTokens(tokens: TokenInfo[], skipCacheRevalidation?: boolean): Promise<void> {
    const tokensToSave: { data: TokenInfoFromStorage; id: string }[] = []

    for (const token of tokens) {
      const { id } = token

      try {
        const rate = await this.getStoredRateById(id)

        if (!rate) throw new Error('Missing rate')

        tokensToSave.push({ data: unmapTokenInfo(token, rate), id })
      } catch (e) {
        this.logger.error(`Failed to save token ${token.id}, error: ${getErrorMessage(e)}`, e)
      }
    }

    await this.firebase.saveDocuments('tokens', tokensToSave)
    this.logger.log(`Saved tokens ${tokensToSave.map((r) => r.id).join(', ')}`)

    if (!skipCacheRevalidation) await this.revalidateCache()
  }

  async addTokens(tokens: TokenToCreate[], skipCacheRevalidation?: boolean): Promise<TokenInfo[]> {
    const tokensToSave: { data: TokenInfoFromStorage; id: string }[] = []
    const savedTokens: TokenInfo[] = []

    for (const tokenData of tokens) {
      const { tokenInfo, rate } = tokenData

      try {
        const id = this.firebase.createDocId('tokens')
        const rateToStore: StoredRate = {
          tokenId: id,
          rate: rate.rate,
          percentChange: rate.percentChange,
          rateHistory: rate.rateHistory,
          isTradable: tokenInfo.isTradable,
          updatedAt: new Date().toISOString(),
        }

        if (!rateToStore) throw new Error('Missing rate')

        const token = { ...tokenInfo, id }
        tokensToSave.push({ data: unmapTokenInfo(token, rateToStore), id })
        savedTokens.push(token)
      } catch (e) {
        this.logger.error(`Failed to add token ${tokenInfo.ticker}, error: ${getErrorMessage(e)}`, e)
      }
    }

    await this.firebase.saveDocuments('tokens', tokensToSave)
    this.logger.log(`Added tokens ${tokensToSave.map((r) => r.id).join(', ')}`)

    if (!skipCacheRevalidation) await this.revalidateCache()

    return savedTokens
  }

  async updateMarketData(data: (MarketData & { tokenId: string })[]): Promise<void> {
    const formattedRates = data.reduce(
      (acc, marketData) => {
        try {
          const parsedMarketData = deleteUndefinedFields(marketDataFromStorageSchema.parse(marketData))

          if (!parsedMarketData.tokenRank) throw new Error('Missing token rank')

          acc.push({ data: { marketData: parsedMarketData as MarketDataFromStorage }, id: marketData.tokenId })
        } catch (e) {
          this.logger.error(
            `Failed to save market data for token ${marketData.tokenId}, error: ${getErrorMessage(e)}`,
            e,
          )
        }

        return acc
      },
      [] as { data: { marketData: MarketDataFromStorage }; id: string }[],
    )

    await this.firebase.saveDocuments('tokens', formattedRates, true)
    this.logger.log(`Saved market data for tokens ${data.map((r) => r.tokenId).join(', ')}`)

    await this.revalidateCache()
  }

  async updateRates(rates: Rate[]): Promise<void> {
    const formattedRates = rates.reduce(
      (acc, rate) => {
        try {
          const rateData = rateFromStorageSchema.parse({
            rate: rate.rate,
            percentChange: rate.percentChange,
            '1d': rate.rateHistory?.map((h) => ({ rate: h.value, timestamp: h.timestamp })),
            updatedAt: new Date().toISOString(),
          })

          acc.push({ data: { rateData }, id: rate.tokenId })
        } catch (e) {
          this.logger.error(`Failed to save rate for token ${rate.tokenId}, error: ${getErrorMessage(e)}`, e)
        }

        return acc
      },
      [] as { data: { rateData: TokenInfoFromStorage['rateData'] }; id: string }[],
    )

    await this.firebase.saveDocuments('tokens', formattedRates, true)
    this.logger.log(`Saved rates for tokens ${rates.map((r) => r.tokenId).join(', ')}`)

    await this.revalidateCache()
  }

  async disableTokens(ids: string[]): Promise<void> {
    const data = ids.map((id) => ({ id, data: { isDisabled: true } }))
    await this.firebase.saveDocuments('tokens', data, true)
    this.logger.log(`Disabled tokens ${ids.join(', ')}`)

    await this.revalidateCache()
  }

  async deleteTokens(ids: string[]): Promise<void> {
    await this.firebase.deleteDocuments('tokens', ids)
    this.logger.log(`Delete tokens ${ids.join(', ')}`)

    await this.revalidateCache()
  }

  private async getTokensFromStorage(): Promise<TokenInfoFromStorage[]> {
    const tokens = await this.firebase.getAllDocuments<TokenInfoFromStorage>('tokens')
    const checkedTokens: TokenInfoFromStorage[] = []

    if (tokens) {
      tokens.forEach((token) => {
        const tokenFromStorageResult = tokenInfoFromStorageSchema.safeParse(token)

        if (tokenFromStorageResult.success) {
          checkedTokens.push(token)
        } else {
          this.logger.error(
            `Incorrect token stored ${token.id}, data: ${JSON.stringify(token)}, tokenFromStorageParseError: ${JSON.stringify(tokenFromStorageResult.error?.errors)}`,
          )
        }
      })
    }

    return checkedTokens
  }

  private async fetchTokens(): Promise<void> {
    try {
      const tokensFromStorage = await this.getTokensFromStorage()

      if (!tokensFromStorage.length) throw new Error('Tokens not found')

      const tradableTokens: TokenInfo[] = []
      const tradableRates: StoredRate[] = []

      const nonTradableTokens: TokenInfo[] = []
      const nonTradableRates: StoredRate[] = []

      const tokensMap = new Map<string, TokenInfo>()
      const ratesMap = new Map<string, StoredRate>()

      const disabledTokens: string[] = []

      tokensFromStorage.forEach((doc) => {
        if (doc.isDisabled) {
          disabledTokens.push(doc.id)

          return
        }

        const tokenInfo = mapTokenInfo(doc)
        const tokenParseResult = tokenInfoSchema.safeParse(tokenInfo.tokenInfo)
        const rateParseResult = rateSchema.safeParse(tokenInfo.rate)

        if (tokenParseResult.success && rateParseResult.success) {
          if (tokenInfo.tokenInfo.isTradable) {
            tradableTokens.push(tokenInfo.tokenInfo)
            tradableRates.push(tokenInfo.rate)
          } else {
            nonTradableTokens.push(tokenInfo.tokenInfo)
            nonTradableRates.push(tokenInfo.rate)
          }

          tokensMap.set(tokenInfo.tokenInfo.id, tokenInfo.tokenInfo)
          ratesMap.set(tokenInfo.tokenInfo.id, tokenInfo.rate)

          return
        }

        this.logger.error(
          `Cannot parse token info for id ${doc.id}, tokenParseError: ${JSON.stringify(tokenParseResult.error?.errors)}, rateParseError: ${JSON.stringify(rateParseResult.error?.errors)}`,
        )
      })

      this.tradableTokens = tradableTokens
      this.tradableRates = tradableRates
      this.nonTradableTokens = nonTradableTokens
      this.nonTradableRates = nonTradableRates
      this.tokensMap = tokensMap
      this.ratesMap = ratesMap
      this.disabledTokens = disabledTokens

      this.setIsInitialized()
      this.logger.log('Fetched tokens from firebase')
    } catch (e) {
      this.logger.error(`Failed to fetch tokens from firebase: ${getErrorMessage(e)}`)
      this.isInitializing = false
    }
  }
}
