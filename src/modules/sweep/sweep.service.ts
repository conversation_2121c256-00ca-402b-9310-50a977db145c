import { Injectable, Inject, Logger, forwardRef } from '@nestjs/common'
import { eq, and, or } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'
import { Address } from 'viem'

import { getEnvConfig } from '@/config/env'
import { CryptoService } from '@/libs/crypto/crypto.service'
import * as schema from '@/libs/database/schema'
import { depositAddresses, projects } from '@/libs/database/schema'

import { BlockchainService } from '../blockchain/blockchain.service'
import { PoolService } from '../pool/pool.service'
import { ProjectNotificationService } from '../projects/services/project-notification.service'

export interface SweepResult {
  success: boolean
  txHash?: string
  error?: string
  amountSwept?: string
  expectedAmount?: string
  status?: 'pending' | 'swept'
  launchFeePaid?: boolean
  totalAvailable?: string
  poolAddress?: string
  poolCreation?: {
    attempted: boolean
    success: boolean
    poolAddress?: string
    txHash?: string
    error?: string
  }
}

@Injectable()
export class SweepService {
  private readonly logger = new Logger(SweepService.name)
  private readonly envConfig = getEnvConfig()

  constructor(
    @Inject('DB') private readonly db: NodePgDatabase<typeof schema>,
    private readonly cryptoService: CryptoService,
    private readonly blockchainService: BlockchainService,
    @Inject(forwardRef(() => PoolService)) private readonly poolService: PoolService,
    private readonly projectNotificationService: ProjectNotificationService,
  ) {}

  /**
   * Main sweep function that checks all pending addresses and sweeps available funds
   */
  async sweepAllPendingAddresses(): Promise<{
    swept: number
    failed: number
    results: { addressId: string; result: SweepResult }[]
  }> {
    this.logger.log('Starting sweep operation for all pending addresses')

    // Get all addresses that are pending (waiting for payment/sweep)
    const addressesToSweep = await this.db.query.depositAddresses.findMany({
      where: and(eq(depositAddresses.paymentStatus, 'pending'), eq(depositAddresses.isActive, true)),
    })

    // Filter out addresses that have already received the full expected amount
    const addressesNeedingSweep = addressesToSweep.filter((address) => {
      const amountReceived = parseFloat(address.amountReceived || '0')
      const expectedAmount = this.envConfig.wallets.sweep.expectedUsdcAmount

      return amountReceived < expectedAmount
    })

    this.logger.log(
      `Found ${addressesNeedingSweep.length} addresses needing sweep (${addressesToSweep.length} total checked)`,
    )

    const results: { addressId: string; result: SweepResult }[] = []
    let swept = 0
    let failed = 0

    for (const address of addressesNeedingSweep) {
      try {
        this.logger.log(`Processing sweep for both EVM and Solana addresses for project: ${address.projectId}`)

        // Sweep both EVM and Solana addresses
        const [evmResult, solanaResult] = await Promise.allSettled([
          this.sweepEvmAddress(address),
          this.sweepSolanaAddress(address),
        ])

        // Process EVM result
        const evmSweepResult: SweepResult =
          evmResult.status === 'fulfilled'
            ? evmResult.value
            : { success: false, error: evmResult.reason?.message || 'EVM sweep failed' }

        // Process Solana result
        const solanaSweepResult: SweepResult =
          solanaResult.status === 'fulfilled'
            ? solanaResult.value
            : { success: false, error: solanaResult.reason?.message || 'Solana sweep failed' }

        // Combine results
        const combinedResult = this.combineSweepResults(evmSweepResult, solanaSweepResult, address)

        // Always attempt pool creation for each project
        const poolCreationResult = await this.triggerPoolCreationAfterFeeDetection(address.projectId)
        combinedResult.poolAddress = poolCreationResult.poolAddress
        combinedResult.poolCreation = poolCreationResult

        results.push({
          addressId: address.id,
          result: combinedResult,
        })

        if (combinedResult.success) {
          swept += 1
          // Extract individual chain transaction hashes
          const evmTxHash = evmSweepResult.success ? evmSweepResult.txHash : undefined
          const solanaTxHash = solanaSweepResult.success ? solanaSweepResult.txHash : undefined

          // Update database status to swept
          await this.updateSweepStatus(address.id, combinedResult.amountSwept || '0', evmTxHash, solanaTxHash)
          this.logger.log(
            `Successfully swept ${combinedResult.amountSwept} total from both chains for project ${address.projectId}`,
          )
        } else {
          failed += 1
          this.logger.error(
            `Failed to sweep from both chains for project ${address.projectId}: ${combinedResult.error}`,
          )
        }
      } catch (error) {
        failed += 1
        this.logger.error(`Error processing addresses for project ${address.projectId}:`, error)
        results.push({
          addressId: address.id,
          result: {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
          },
        })
      }
    }

    this.logger.log(`Sweep operation completed. Swept: ${swept}, Failed: ${failed}`)

    return { swept, failed, results }
  }

  /**
   * Combine EVM and Solana sweep results and determine status based on total amount
   */
  private combineSweepResults(evmResult: SweepResult, solanaResult: SweepResult, address: any): SweepResult {
    const txHashes: string[] = []
    let totalAmountSwept = 0

    if (evmResult.success && evmResult.txHash) {
      txHashes.push(`EVM:${evmResult.txHash}`)
      totalAmountSwept += parseFloat(evmResult.amountSwept || '0')
    }

    if (solanaResult.success && solanaResult.txHash) {
      txHashes.push(`Solana:${solanaResult.txHash}`)
      totalAmountSwept += parseFloat(solanaResult.amountSwept || '0')
    }

    // Consider success if any amount was actually swept
    const success = totalAmountSwept > 0

    // Determine status based on total amount (existing + newly swept)
    const existingAmount = parseFloat(address.amountReceived || '0')
    const totalAmount = existingAmount + totalAmountSwept
    const expectedAmount = this.envConfig.wallets.sweep.expectedUsdcAmount

    let status: 'pending' | 'swept' = 'pending'

    if (success && totalAmount >= expectedAmount && expectedAmount > 0) {
      status = 'swept'
    }

    return {
      success,
      txHash: txHashes.length > 0 ? txHashes.join(', ') : '',
      amountSwept: totalAmountSwept.toString(),
      expectedAmount: expectedAmount.toString(),
      status,
    }
  }

  /**
   * Sweep funds from EVM address using permit (no gas needed for deposit address)
   */
  private async sweepEvmAddress(address: any): Promise<SweepResult> {
    this.logger.log(`Checking all chains for USDC balance on EVM address: ${address.evmPublicAddress}`)

    const sweepResults: { chain: string; result: SweepResult }[] = []
    let totalSwept = 0
    const txHashes: string[] = []

    const evmClients = this.blockchainService.getAllEvmClients()

    // Check all supported chains
    for (const [chainName] of Object.entries(evmClients)) {
      try {
        this.logger.log(`Checking ${chainName} for USDC balance`)

        // Check USDC balance
        const balanceFormatted = await this.blockchainService.checkEvmUsdcBalance(
          chainName as any,
          address.evmPublicAddress as Address,
        )

        if (balanceFormatted === 0) {
          this.logger.log(`No USDC balance on ${chainName}, skipping`)
          continue
        }

        // Sweep from this chain
        const decryptedPrivateKey = await this.decryptPrivateKey(address.evmEncryptedPrivateKey)
        const chainSweepResult = await this.blockchainService.sweepFromEvmChain(
          chainName as any,
          address.evmPublicAddress as Address,
          decryptedPrivateKey,
          this.envConfig.wallets.sweep.evmAddress as Address,
          BigInt(Math.floor(balanceFormatted * 1e6)),
        )

        const sweepResult: SweepResult = {
          success: chainSweepResult.success,
          txHash: chainSweepResult.txHash,
          error: chainSweepResult.error,
          amountSwept: balanceFormatted.toString(),
          expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
        }

        sweepResults.push({ chain: chainName, result: sweepResult })

        if (chainSweepResult.success) {
          totalSwept += parseFloat(sweepResult.amountSwept || '0')

          if (chainSweepResult.txHash) {
            txHashes.push(`${chainName}:${chainSweepResult.txHash}`)
          }
        }
      } catch (error) {
        this.logger.error(`Error checking ${chainName}:`, error)

        sweepResults.push({
          chain: chainName,
          result: {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
          },
        })
      }
    }

    // Determine overall result
    const successfulSweeps = sweepResults.filter((r) => r.result.success)
    const failedSweeps = sweepResults.filter((r) => !r.result.success)

    if (successfulSweeps.length === 0) {
      const errors = failedSweeps.map((f) => `${f.chain}: ${f.result.error}`).join('; ')

      return {
        success: false,
        error: failedSweeps.length > 0 ? errors : 'No USDC balance found on any EVM chain',
        expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
      }
    }

    this.logger.log(`Successfully swept from ${successfulSweeps.length} EVM chains. Total: ${totalSwept} USDC`)

    return {
      success: true,
      txHash: txHashes.join(', '),
      amountSwept: totalSwept.toString(),
      expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
    }
  }

  /**
   * Sweep funds from Solana address using gasless fee payer pattern
   */
  private async sweepSolanaAddress(address: any): Promise<SweepResult> {
    try {
      // Decrypt the deposit address private key
      const decryptedPrivateKey = await this.decryptPrivateKey(address.solanaEncryptedPrivateKey)

      // Use blockchain service to sweep from Solana
      const result = await this.blockchainService.sweepFromSolana(
        address.solanaPublicAddress,
        decryptedPrivateKey,
        this.envConfig.wallets.sweep.solanaAddress,
        this.envConfig.wallets.sweep.solanaPrivateKey,
      )

      return {
        success: result.success,
        txHash: result.txHash,
        error: result.error,
        amountSwept: result.amountSwept || '0',
        expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
      }
    } catch (error) {
      this.logger.error('Error sweeping Solana address:', error)

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
      }
    }
  }

  /**
   * Decrypt private key using the crypto service
   */
  private async decryptPrivateKey(encryptedPrivateKey: string): Promise<string> {
    return this.cryptoService.decryptPrivateKey(encryptedPrivateKey)
  }

  /**
   * Update sweep status in database
   */
  private async updateSweepStatus(
    addressId: string,
    newAmountSwept: string,
    evmTxHash?: string,
    solanaTxHash?: string,
  ) {
    // First, get the current record to check existing amounts and tx hashes
    const existingRecord = await this.db.query.depositAddresses.findFirst({
      where: eq(depositAddresses.id, addressId),
    })

    if (!existingRecord) {
      throw new Error(`Address record not found: ${addressId}`)
    }

    const {
      amountReceived: currentAmountReceivedStr,
      evmSweepTxHash,
      solanaSweepTxHash,
      paymentStatus: currentStatus,
    } = existingRecord

    const currentAmountReceived = parseFloat(currentAmountReceivedStr || '0')
    const newAmount = parseFloat(newAmountSwept)
    const totalAmountReceived = currentAmountReceived + newAmount
    const expectedAmount = this.envConfig.wallets.sweep.expectedUsdcAmount

    // Accumulate transaction hashes
    const existingEvmTxHashes = evmSweepTxHash ? evmSweepTxHash.split(', ') : []
    const existingSolanaTxHashes = solanaSweepTxHash ? solanaSweepTxHash.split(', ') : []

    if (evmTxHash && !existingEvmTxHashes.includes(evmTxHash)) {
      existingEvmTxHashes.push(evmTxHash)
    }

    if (solanaTxHash && !existingSolanaTxHashes.includes(solanaTxHash)) {
      existingSolanaTxHashes.push(solanaTxHash)
    }

    // Determine payment status and launch fee paid based on whether we've received the full expected amount
    let paymentStatus = currentStatus
    const launchFeePaid = totalAmountReceived >= expectedAmount && expectedAmount > 0

    // Only set status to 'swept' if we have actual on-chain transaction hashes
    // indicating funds were successfully moved
    if (evmTxHash || solanaTxHash || existingEvmTxHashes.length > 0 || existingSolanaTxHashes.length > 0) {
      paymentStatus = 'swept'
    } else if (launchFeePaid) {
      // If launch fee is paid but no sweeps yet, keep as pending or set to a new status
      paymentStatus = 'pending' // Funds detected but not yet swept
    }

    const updates: any = {
      paymentStatus,
      amountReceived: totalAmountReceived.toString(),
      launchFeePaid,
      lastSweptAt: new Date(),
      updatedAt: new Date(),
    }

    // Update transaction hashes
    if (existingEvmTxHashes.length > 0) {
      updates.evmSweepTxHash = existingEvmTxHashes.join(', ')
    }

    if (existingSolanaTxHashes.length > 0) {
      updates.solanaSweepTxHash = existingSolanaTxHashes.join(', ')
    }

    this.logger.log(
      `Updating sweep status for ${addressId}: Status=${paymentStatus}, Launch Fee Paid=${launchFeePaid}, Total Amount=${totalAmountReceived}/${expectedAmount}, EVM TXs=${existingEvmTxHashes.length}, Solana TXs=${existingSolanaTxHashes.length}`,
    )

    const result = await this.db
      .update(depositAddresses)
      .set(updates)
      .where(eq(depositAddresses.id, addressId))
      .returning()

    // Pool creation is now handled separately at fee detection time, not sweep completion time
    this.logger.log(`Sweep status updated for ${addressId}. Pool creation handled separately.`)

    return { result }
  }

  /**
   * Update project status to fee_paid when launch fee is confirmed
   */
  private async updateProjectStatusToFeePaid(projectId: string): Promise<void> {
    try {
      const project = await this.db.query.projects.findFirst({
        where: eq(projects.projectId, projectId),
      })

      if (!project) {
        this.logger.error(`Project ${projectId} not found when updating status to fee_paid`)

        return
      }

      // Only update status if current status allows it (can transition from approved or wallet_provided)
      if (project.submissionStatus === 'approved' || project.submissionStatus === 'wallet_provided') {
        await this.db
          .update(projects)
          .set({
            submissionStatus: 'fee_paid',
            updatedAt: new Date(),
          })
          .where(eq(projects.projectId, projectId))

        this.logger.log(`Updated project ${projectId} status from '${project.submissionStatus}' to 'fee_paid'`)

        // Send fee payment confirmation email when status is successfully updated
        try {
          // Get user data for email
          const userResult = await this.db.query.users.findFirst({
            where: eq(schema.users.id, project.userId),
          })

          if (userResult) {
            const projectData = {
              projectId: project.projectId,
              projectName: project.projectName,
              tokenSymbol: project.tokenSymbol,
              expectedTgeLaunchDate: project.expectedTgeLaunchDate
                ? new Date(project.expectedTgeLaunchDate).toISOString()
                : new Date().toISOString(),
            }

            const userData = {
              id: userResult.id,
              email: userResult.email,
              name: userResult.name,
            }

            await this.projectNotificationService.sendFeePaymentConfirmationEmail(projectData, userData)
            this.logger.log(`Fee payment confirmation email sent for project ${projectId}`)
          } else {
            this.logger.error(`User not found for project ${projectId} - cannot send email`)
          }
        } catch (emailError) {
          this.logger.error(`Failed to send fee payment confirmation email for project ${projectId}:`, emailError)
          // Don't throw - email failure shouldn't prevent status update
        }
      } else {
        this.logger.log(`Project ${projectId} status is '${project.submissionStatus}', not updating to 'fee_paid'`)
      }
    } catch (error) {
      this.logger.error(`Failed to update project ${projectId} status to fee_paid:`, error)
    }
  }

  /**
   * Update project status to liquidity_provided when pool is created
   */
  private async updateProjectStatusToLiquidityProvided(projectId: string): Promise<void> {
    try {
      const project = await this.db.query.projects.findFirst({
        where: eq(projects.projectId, projectId),
      })

      if (!project) {
        this.logger.error(`Project ${projectId} not found when updating status to liquidity_provided`)

        return
      }

      // Only update status if current status allows it
      if (project.submissionStatus === 'fee_paid') {
        await this.db
          .update(projects)
          .set({
            submissionStatus: 'liquidity_provided',
            updatedAt: new Date(),
          })
          .where(eq(projects.projectId, projectId))

        this.logger.log(
          `Updated project ${projectId} status from '${project.submissionStatus}' to 'liquidity_provided'`,
        )
      } else {
        this.logger.log(
          `Project ${projectId} status is '${project.submissionStatus}', not updating to 'liquidity_provided'`,
        )
      }
    } catch (error) {
      this.logger.error(`Failed to update project ${projectId} status to liquidity_provided:`, error)
    }
  }

  /**
   * Get sweep status for all addresses
   */
  async getSweepStatus() {
    const addresses = await this.db.query.depositAddresses.findMany({
      orderBy: (addr, { desc }) => [desc(addr.createdAt)],
    })

    const summary = {
      pending: addresses.filter((addr) => addr.paymentStatus === 'pending').length,
      swept: addresses.filter((addr) => addr.paymentStatus === 'swept').length,
      launchFeePaid: addresses.filter((addr) => addr.launchFeePaid).length,
      launchFeePending: addresses.filter((addr) => !addr.launchFeePaid).length,
      total: addresses.length,
    }

    return {
      summary,
      addresses,
    }
  }

  /**
   * Check balances on all addresses without sweeping
   */
  private async checkTotalAvailableBalance(
    address: any,
  ): Promise<{ totalAvailable: number; evmBalance: number; solanaBalance: number }> {
    const balances = await this.blockchainService.checkAllUsdcBalances(
      address.evmPublicAddress as Address,
      address.solanaPublicAddress,
    )

    return {
      totalAvailable: balances.total,
      evmBalance: balances.evm,
      solanaBalance: balances.solana,
    }
  }

  /**
   * Update launch fee paid status based on total available balance
   */
  private async updateLaunchFeePaidStatus(addressId: string, totalAvailable: number): Promise<boolean> {
    const existingRecord = await this.db.query.depositAddresses.findFirst({
      where: eq(depositAddresses.id, addressId),
    })

    if (!existingRecord) {
      throw new Error(`Address record not found: ${addressId}`)
    }

    const currentAmountReceived = parseFloat(existingRecord.amountReceived || '0')
    const totalAmount = currentAmountReceived + totalAvailable
    const expectedAmount = this.envConfig.wallets.sweep.expectedUsdcAmount
    const launchFeePaid = totalAmount >= expectedAmount && expectedAmount > 0

    // Update the launch fee paid status if it has changed
    if (launchFeePaid !== existingRecord.launchFeePaid) {
      await this.db
        .update(depositAddresses)
        .set({
          launchFeePaid,
          updatedAt: new Date(),
        })
        .where(eq(depositAddresses.id, addressId))

      this.logger.log(
        `Updated launch fee paid status for ${addressId}: ${launchFeePaid} (Total: ${totalAmount}/${expectedAmount})`,
      )

      // If launch fee is now fully paid, update project status and potentially trigger pool creation
      if (launchFeePaid) {
        this.logger.log(`Launch fee detected as paid for project ${existingRecord.projectId}`)
        this.updateProjectStatusToFeePaid(existingRecord.projectId).catch((error) => {
          this.logger.error(`Failed to update project status to fee_paid for ${existingRecord.projectId}:`, error)
        })
      }
    }

    return launchFeePaid
  }

  /**
   * Force sweep a specific project by EVM or Solana public address
   */
  async sweepByPublicAddress(publicAddress: string): Promise<SweepResult> {
    // Try to find by either EVM or Solana address
    const address = await this.db.query.depositAddresses.findFirst({
      where: or(
        eq(depositAddresses.evmPublicAddress, publicAddress),
        eq(depositAddresses.solanaPublicAddress, publicAddress),
      ),
    })

    if (!address) {
      return {
        success: false,
        error: 'Address not found',
        expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
        launchFeePaid: false,
      }
    }

    const expectedAmount = this.envConfig.wallets.sweep.expectedUsdcAmount
    const currentlySwept = parseFloat(address.amountReceived || '0')

    // Check available balances immediately
    const { totalAvailable } = await this.checkTotalAvailableBalance(address)

    // Update launch fee paid status
    const launchFeePaid = await this.updateLaunchFeePaidStatus(address.id, totalAvailable)

    this.logger.log(
      `Launch fee status for ${publicAddress}: ${launchFeePaid ? 'PAID' : 'PENDING'} (Available: ${totalAvailable}, Swept: ${currentlySwept}, Expected: ${expectedAmount})`,
    )

    // Always attempt pool creation (create if doesn't exist, return existing if it does)
    this.logger.log(`Attempting pool creation for project ${address.projectId}`)
    const poolCreationResult = await this.triggerPoolCreationAfterFeeDetection(address.projectId)

    // Prepare immediate response
    const immediateResponse: SweepResult = {
      success: true,
      launchFeePaid,
      totalAvailable: (currentlySwept + totalAvailable).toString(),
      expectedAmount: expectedAmount.toString(),
      amountSwept: '0', // Will be updated after sweep
      poolAddress: poolCreationResult.poolAddress,
      poolCreation: poolCreationResult,
    }

    // Continue with sweep operation in background if there's balance to sweep
    if (totalAvailable > 0) {
      this.performBackgroundSweep(address).catch((error) => {
        this.logger.error(`Background sweep failed for ${publicAddress}:`, error)
      })
    }

    return immediateResponse
  }

  /**
   * Trigger pool creation (create if doesn't exist, return existing if it does)
   */
  private async triggerPoolCreationAfterFeeDetection(projectId: string): Promise<{
    attempted: boolean
    success: boolean
    poolAddress?: string
    txHash?: string
    error?: string
  }> {
    try {
      // Check if project already has a pool address
      const project = await this.db.query.projects.findFirst({
        where: eq(projects.projectId, projectId),
      })

      if (project?.poolAddress) {
        this.logger.log(`Project ${projectId} already has pool address: ${project.poolAddress}`)

        return {
          attempted: true,
          success: true,
          poolAddress: project.poolAddress,
          error: 'Pool already exists',
        }
      }

      // Update project status to fee_paid if not already
      await this.updateProjectStatusToFeePaid(projectId)

      // Attempt pool creation
      const poolResult = await this.poolService.createPoolAutomatic(projectId)

      if (poolResult.success) {
        this.logger.log(
          `Pool creation successful for project ${projectId}. Pool address: ${poolResult.poolAddress}, TX: ${poolResult.txHash}`,
        )

        return {
          attempted: true,
          success: true,
          poolAddress: poolResult.poolAddress,
          txHash: poolResult.txHash,
        }
      }

      const isSkipped = poolResult.error?.startsWith('Skipped:')

      if (isSkipped) {
        this.logger.warn(`Pool creation skipped for project ${projectId}: ${poolResult.error}`)
      } else {
        this.logger.error(
          `Pool creation failed for project ${projectId}: ${poolResult.error}`,
          poolResult.debugInfo ? { debugInfo: poolResult.debugInfo } : undefined,
        )
      }

      return {
        attempted: true,
        success: false,
        error: poolResult.error,
      }
    } catch (error) {
      this.logger.error(`Error in pool creation for project ${projectId}:`, error)

      return {
        attempted: true,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  /**
   * Perform sweep operation in the background
   */
  private async performBackgroundSweep(address: any): Promise<void> {
    this.logger.log(`Starting background sweep for project: ${address.projectId}`)

    // Sweep both EVM and Solana addresses
    const [evmResult, solanaResult] = await Promise.allSettled([
      this.sweepEvmAddress(address),
      this.sweepSolanaAddress(address),
    ])

    // Process results
    const evmSweepResult: SweepResult =
      evmResult.status === 'fulfilled'
        ? evmResult.value
        : { success: false, error: evmResult.reason?.message || 'EVM sweep failed' }

    const solanaSweepResult: SweepResult =
      solanaResult.status === 'fulfilled'
        ? solanaResult.value
        : { success: false, error: solanaResult.reason?.message || 'Solana sweep failed' }

    // Combine results
    const combinedResult = this.combineSweepResults(evmSweepResult, solanaSweepResult, address)

    if (combinedResult.success) {
      // Extract individual chain transaction hashes
      const evmTxHash = evmSweepResult.success ? evmSweepResult.txHash : undefined
      const solanaTxHash = solanaSweepResult.success ? solanaSweepResult.txHash : undefined

      await this.updateSweepStatus(address.id, combinedResult.amountSwept || '0', evmTxHash, solanaTxHash)

      this.logger.log(
        `Background sweep completed for project ${address.projectId}. Amount: ${combinedResult.amountSwept}`,
      )
    } else {
      this.logger.error(`Background sweep failed for project ${address.projectId}: ${combinedResult.error}`)
    }
  }

  /**
   * Force sweep a specific address by ID
   */
  async sweepSpecificAddress(addressId: string): Promise<SweepResult> {
    const address = await this.db.query.depositAddresses.findFirst({
      where: eq(depositAddresses.id, addressId),
    })

    if (!address) {
      return {
        success: false,
        error: 'Address not found',
        expectedAmount: this.envConfig.wallets.sweep.expectedUsdcAmount.toString(),
        launchFeePaid: false,
      }
    }

    const expectedAmount = this.envConfig.wallets.sweep.expectedUsdcAmount
    const currentlySwept = parseFloat(address.amountReceived || '0')

    // Check available balances immediately
    const { totalAvailable } = await this.checkTotalAvailableBalance(address)

    // Update launch fee paid status and return immediately
    const launchFeePaid = await this.updateLaunchFeePaidStatus(address.id, totalAvailable)

    this.logger.log(
      `Launch fee status for ${addressId}: ${launchFeePaid ? 'PAID' : 'PENDING'} (Available: ${totalAvailable}, Swept: ${currentlySwept}, Expected: ${expectedAmount})`,
    )

    // Always attempt pool creation (create if doesn't exist, return existing if it does)
    this.logger.log(`Attempting pool creation for project ${address.projectId}`)
    const poolCreationResult = await this.triggerPoolCreationAfterFeeDetection(address.projectId)

    // Prepare immediate response
    const immediateResponse: SweepResult = {
      success: true,
      launchFeePaid,
      totalAvailable: (currentlySwept + totalAvailable).toString(),
      expectedAmount: expectedAmount.toString(),
      amountSwept: '0', // Will be updated after sweep
      poolAddress: poolCreationResult.poolAddress,
      poolCreation: poolCreationResult,
    }

    // Continue with sweep operation in background if there's balance to sweep
    if (totalAvailable > 0) {
      this.performBackgroundSweep(address).catch((error) => {
        this.logger.error(`Background sweep failed for ${addressId}:`, error)
      })
    }

    return immediateResponse
  }
}
