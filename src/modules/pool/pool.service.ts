import { Raydium, CLMM_PROGRAM_ID, getPdaAmmConfigId } from '@inclusivelayer/raydium-sdk-v2'
import { Injectable, Logger, Inject, BadRequestException, NotFoundException, OnModuleInit } from '@nestjs/common'
import { PublicKey, Keypair } from '@solana/web3.js'
import bs58 from 'bs58'
import { eq, type InferSelectModel } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'
import { Address, getContract, encodeFunctionData } from 'viem'

import { getEnvConfig } from '@/config/env'
import * as schema from '@/libs/database/schema'
import { projects, depositAddresses } from '@/libs/database/schema'

import { peripheryAbi } from './abi/periphery'
import { CreatePoolDto } from './dto/create-pool.dto'
import { tgePriceToSqrtPriceX96, validateTgePrice } from './utils/price-utils'
import { BlockchainService, SupportedChain, USDC_CONTRACTS } from '../blockchain/blockchain.service'

// Type for Project records from database
type Project = InferSelectModel<typeof projects>

// Solana constants for pool creation
const SOLANA_USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
const HYPERLANE_PROGRAM_ID = new PublicKey('E588QtVUvresuXq2KoNEwAmoifCzYGpRBdHByN9KQMbi')
const VAULT_PROGRAM_ID = new PublicKey('2RBS3DPck8CoF9b31nQDRE3j9xsx5io1STAk2irhgoBC')
const SPL_NOOP_PROGRAM_ID = new PublicKey('noopb9bkMVfRPU8AsbpTUg8AQkHtKwMYZiFUjNRtMmV')

export interface LaunchParams {
  tokenLaunchType: number
  exclusiveTradingPeriodStart: bigint
  exclusiveTradingPeriodEnd: bigint
  extendedLiquidityLockDuration: bigint
  whitelistedAddressForSwap: Address
  projectManager: Address
}

export interface PoolConfig {
  chain: 'solana' | SupportedChain
  usdcAddress: string
  projectTokenAddress: string
  feeTier: number
  tgePrice: number
  launchParams: LaunchParams
}

// Contract addresses for each chain
const UNISWAP_ADDRESSES = {
  mainnet: {
    periphery: '0x0F3E5f3D419CbeB1c2B72F290c18202D39ab5b82' as Address,
    factory: '0xC4807E33650F63A2551dE2cFB5eE06C5E5C04cD6' as Address,
  },
  arbitrum: {
    periphery: '0x0F3E5f3D419CbeB1c2B72F290c18202D39ab5b82' as Address,
    factory: '0xC4807E33650F63A2551dE2cFB5eE06C5E5C04cD6' as Address,
  },
  base: {
    periphery: '0x0F3E5f3D419CbeB1c2B72F290c18202D39ab5b82' as Address,
    factory: '0xC4807E33650F63A2551dE2cFB5eE06C5E5C04cD6' as Address,
  },
} as const

// ABI for the factory contract
const factoryAbi = [
  {
    type: 'function',
    name: 'getPool',
    inputs: [
      { name: 'tokenA', type: 'address' },
      { name: 'tokenB', type: 'address' },
      { name: 'fee', type: 'uint24' },
    ],
    outputs: [{ name: 'pool', type: 'address' }],
    stateMutability: 'view',
  },
] as const

export interface CreatePoolResult {
  success: boolean
  chain: string
  poolAddress?: string
  txHash?: string
  error?: string
  debugInfo?: {
    to?: string
    from?: string
    data?: string
  }
}

@Injectable()
export class PoolService implements OnModuleInit {
  private readonly logger = new Logger(PoolService.name)
  private readonly envConfig = getEnvConfig()
  private raydiumInstance: Raydium | null = null

  constructor(
    private readonly blockchainService: BlockchainService,
    @Inject('DB') private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async onModuleInit() {
    await this.initializeRaydiumSdk()
  }

  /**
   * Initialize Raydium SDK instance
   */
  private async initializeRaydiumSdk() {
    try {
      this.logger.log('Initializing Raydium SDK...')

      // Get Solana connection from blockchain service
      const connection = this.blockchainService.getSolanaConnection()

      // Create pool manager keypair from private key
      const poolManagerPrivateKey = this.envConfig.wallets.poolManager.solanaPrivateKey

      if (!poolManagerPrivateKey) {
        throw new Error('Pool manager Solana private key not configured')
      }

      const poolManagerKeypair = Keypair.fromSecretKey(bs58.decode(poolManagerPrivateKey))

      // Initialize Raydium SDK
      this.raydiumInstance = await Raydium.load({
        connection,
        cluster: 'mainnet',
        owner: poolManagerKeypair,
        disableFeatureCheck: true,
        disableLoadToken: false,
        blockhashCommitment: 'finalized',
        urlConfigs: {
          BASE_HOST: 'https://api-v3.raydium.io/',
        },
      })

      this.logger.log('Raydium SDK initialized successfully')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.logger.error(`Failed to initialize Raydium SDK: ${errorMessage}`)
      // Don't throw here - let pool creation methods handle the null instance
    }
  }

  /**
   * Create a pool automatically after fee payment (called by sweep service)
   * More graceful error handling for automatic creation
   */
  async createPoolAutomatic(projectId: string): Promise<CreatePoolResult> {
    this.logger.log('Creating pool automatically for project:', projectId)

    try {
      return await this.createPool({ projectId })
    } catch (error) {
      // More graceful handling for automatic creation
      if (error instanceof BadRequestException) {
        this.logger.warn(`Automatic pool creation skipped for project ${projectId}: ${error.message}`)

        return {
          success: false,
          chain: 'unknown',
          error: `Skipped: ${error.message}`,
        }
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      const errorStack = error instanceof Error ? error.stack : undefined

      this.logger.error(
        `Automatic pool creation failed for project ${projectId}: ${errorMessage}. Details: ${JSON.stringify({
          projectId,
          errorType: error?.constructor?.name || 'Unknown',
          context: 'automatic_creation',
          errorStack: errorStack?.split('\n').slice(0, 3).join(' | '), // First 3 lines of stack
        })}`,
      )

      return {
        success: false,
        chain: 'unknown',
        error: errorMessage,
      }
    }
  }

  /**
   * Create a pool on specified chain - auto-populated from project data
   */
  async createPool(dto: CreatePoolDto): Promise<CreatePoolResult> {
    this.logger.log('Creating pool for project:', dto.projectId)

    try {
      // Fetch project data and validate
      const project = await this.validateProjectForPoolCreation(dto.projectId)

      // Auto-populate config from project data
      const config = await this.buildPoolConfigFromProject(project, dto)

      // Validate TGE price
      validateTgePrice(config.tgePrice)

      // Create pool on the appropriate chain
      let poolResult: CreatePoolResult

      if (config.chain === 'solana') {
        poolResult = await this.createSolanaPool(config)
      } else {
        poolResult = await this.createEvmPool(config)
      }

      // If pool creation was successful, update the project with the pool address
      if (poolResult.success && poolResult.poolAddress) {
        await this.updateProjectPoolAddress(dto.projectId, poolResult.poolAddress)
        this.logger.log(`Updated project ${dto.projectId} with pool address: ${poolResult.poolAddress}`)
      }

      return poolResult
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      const errorStack = error instanceof Error ? error.stack : undefined

      this.logger.error(
        `Pool creation failed for project ${dto.projectId}: ${errorMessage}. Details: ${JSON.stringify({
          projectId: dto.projectId,
          errorType: error?.constructor?.name || 'Unknown',
          dto,
          errorStack: errorStack?.split('\n').slice(0, 3).join(' | '), // First 3 lines of stack
        })}`,
      )

      return {
        success: false,
        chain: 'unknown',
        error: errorMessage,
      }
    }
  }

  /**
   * Validate that the project exists, doesn't already have a pool, and has paid launch fees
   */
  private async validateProjectForPoolCreation(projectId: string): Promise<Project> {
    const project = await this.db.query.projects.findFirst({
      where: eq(projects.projectId, projectId),
    })

    if (!project) {
      throw new NotFoundException(`Project with ID ${projectId} not found`)
    }

    if (project.poolAddress) {
      throw new BadRequestException(`Project ${projectId} already has a pool address: ${project.poolAddress}`)
    }

    // Check if launch fee has been paid
    await this.validateLaunchFeePaid(projectId)

    // Validate project status is appropriate for pool creation
    if (project.submissionStatus !== 'fee_paid' && project.submissionStatus !== 'liquidity_provided') {
      throw new BadRequestException(
        `Project ${projectId} must have 'fee_paid' status to create pool. Current status: ${project.submissionStatus}`,
      )
    }

    return project
  }

  /**
   * Validate that the launch fee has been paid for the project
   */
  private async validateLaunchFeePaid(projectId: string): Promise<void> {
    const depositAddress = await this.db.query.depositAddresses.findFirst({
      where: eq(depositAddresses.projectId, projectId),
    })

    if (!depositAddress) {
      throw new BadRequestException(
        `No deposit address found for project ${projectId}. Launch fee payment is required before pool creation.`,
      )
    }

    if (!depositAddress.launchFeePaid) {
      throw new BadRequestException(
        `Launch fee has not been paid for project ${projectId}. Current amount received: ${depositAddress.amountReceived || '0'} USDC. Pool creation requires full launch fee payment.`,
      )
    }

    this.logger.log(`Launch fee validation passed for project ${projectId}`)
  }

  /**
   * Build pool configuration from project data with optional overrides
   */
  private async buildPoolConfigFromProject(project: Project, dto: CreatePoolDto): Promise<PoolConfig> {
    // Support both Base and Solana chains for pool creation
    if (project.blockchainToLaunchOn !== 'base' && project.blockchainToLaunchOn !== 'solana') {
      throw new BadRequestException(
        `Pool creation is only supported on Base and Solana networks. Current project blockchain: ${project.blockchainToLaunchOn}. Please update your project to use Base or Solana network.`,
      )
    }

    // Map blockchain enum to supported chain names
    const chainMapping: Record<string, SupportedChain | 'solana'> = {
      base: 'base',
      solana: 'solana',
    }

    const chain = chainMapping[project.blockchainToLaunchOn]

    // Get USDC address for the chain
    let usdcAddress: string

    if (chain === 'solana') {
      usdcAddress = SOLANA_USDC_MINT
    } else {
      usdcAddress = USDC_CONTRACTS[chain as SupportedChain]

      if (!usdcAddress) {
        throw new BadRequestException(`USDC not supported on chain: ${chain}`)
      }
    }

    // Validate that provided wallet address exists (required for project manager)
    if (!project.providedWalletAddress) {
      throw new BadRequestException(
        `Project ${project.projectId} must have a provided wallet address to create pool. Please provide a wallet address first.`,
      )
    }

    // Determine the project manager address
    const projectManagerAddress = dto.projectManager || project.providedWalletAddress

    this.logger.log(
      `Project manager determination for ${project.projectId}: dto.projectManager=${dto.projectManager || 'undefined'}, project.providedWalletAddress=${project.providedWalletAddress}, final=${projectManagerAddress}`,
    )

    // Validate that project manager is not zero address
    if (!projectManagerAddress || projectManagerAddress === '******************************************') {
      throw new BadRequestException(
        `Project manager cannot be zero address for project ${project.projectId}. Provided: dto.projectManager=${dto.projectManager || 'undefined'}, project.providedWalletAddress=${project.providedWalletAddress}`,
      )
    }

    // Calculate timestamps from project data
    const launchDate = new Date(project.expectedTgeLaunchDate).getTime() / 1000
    const exclusivePeriodHours = project.exclusiveTradingPeriodHours || 24

    const exclusiveTradingPeriodStart = BigInt(Math.floor(launchDate))
    const exclusiveTradingPeriodEnd = BigInt(Math.floor(launchDate + exclusivePeriodHours * 3600))
    const extendedLiquidityLockDuration = BigInt(24 * 3600) // 24 hours default

    const poolConfig = {
      chain,
      usdcAddress,
      projectTokenAddress: project.tokenContractAddress,
      feeTier: 10000, // Hardcoded to 1% fee for all pools
      tgePrice: parseFloat(project.tgePriceUsdc),
      launchParams: {
        tokenLaunchType: dto.tokenLaunchType || 0,
        exclusiveTradingPeriodStart,
        exclusiveTradingPeriodEnd,
        extendedLiquidityLockDuration,
        whitelistedAddressForSwap: (dto.whitelistedAddressForSwap ||
          '******************************************') as Address,
        projectManager: projectManagerAddress as Address,
      },
    }

    this.logger.log(
      `Pool config created for ${project.projectId}: ${JSON.stringify({
        chain: poolConfig.chain,
        projectManager: poolConfig.launchParams.projectManager,
        feeTier: poolConfig.feeTier,
        tgePrice: poolConfig.tgePrice,
      })}`,
    )

    return poolConfig
  }

  /**
   * Update the project with the pool address (without changing status)
   */
  private async updateProjectPoolAddress(projectId: string, poolAddress: string): Promise<void> {
    try {
      await this.db
        .update(projects)
        .set({
          poolAddress,
          updatedAt: new Date(),
        })
        .where(eq(projects.projectId, projectId))

      this.logger.log(`Updated project ${projectId} with pool address: ${poolAddress} (status unchanged)`)
    } catch (error) {
      this.logger.error(`Failed to update project ${projectId} with pool address:`, error)
      throw new Error('Failed to update project with pool address')
    }
  }

  /**
   * Create pool on EVM chains
   * Placeholder implementation
   */
  private async createEvmPool(config: PoolConfig): Promise<CreatePoolResult> {
    this.logger.log(`Creating EVM pool on ${config.chain}`)

    // Prepare transaction details for debugging
    let txDetails: {
      to?: string
      from?: string
      data?: string
    } = {}

    try {
      const chainName = config.chain as SupportedChain
      const publicClient = this.blockchainService.getPublicClient(chainName)
      const poolManagerClient = this.blockchainService.getPoolManagerClient(chainName)
      const addresses = UNISWAP_ADDRESSES[chainName]

      if (!addresses) {
        throw new Error(`Unsupported chain: ${chainName}`)
      }

      // Set transaction details for debugging
      txDetails = {
        to: addresses.periphery,
        from: poolManagerClient.account!.address,
        data: undefined,
      }

      // Create factory contract instance to check if pool exists
      const factoryContract = getContract({
        address: addresses.factory,
        abi: factoryAbi,
        client: publicClient,
      })

      // Check if pool already exists
      const existingPool = await factoryContract.read.getPool([
        config.usdcAddress as Address,
        config.projectTokenAddress as Address,
        config.feeTier,
      ])

      if (existingPool && existingPool !== '******************************************') {
        this.logger.log(`Pool already exists at address: ${existingPool}`)

        return {
          success: true,
          chain: config.chain,
          poolAddress: existingPool,
          error: 'Pool already exists',
        }
      }

      // Create periphery contract instance for pool creation
      const peripheryContract = getContract({
        address: addresses.periphery,
        abi: peripheryAbi,
        client: poolManagerClient,
      })

      // Convert TGE price to sqrtPriceX96 format
      const sqrtPriceX96 = tgePriceToSqrtPriceX96(config.tgePrice, config.usdcAddress, config.projectTokenAddress)

      // Prepare parameters for pool creation
      const poolParams = {
        token0: config.usdcAddress as Address,
        token1: config.projectTokenAddress as Address,
        fee: config.feeTier,
        sqrtPriceX96: BigInt(sqrtPriceX96),
        launchParams: {
          tokenLaunchType: config.launchParams.tokenLaunchType,
          exclusiveTradingPeriodStart: config.launchParams.exclusiveTradingPeriodStart,
          exclusiveTradingPeriodEnd: config.launchParams.exclusiveTradingPeriodEnd,
          extendedLiquidityLockDuration: config.launchParams.extendedLiquidityLockDuration,
          whitelistedAddressForSwap: config.launchParams.whitelistedAddressForSwap,
          projectManager: config.launchParams.projectManager,
        },
      }

      // Validate project manager in pool params before sending to contract
      if (
        !poolParams.launchParams.projectManager ||
        poolParams.launchParams.projectManager === '******************************************'
      ) {
        throw new Error(
          `CRITICAL: Project manager is zero address in pool params! This should never happen. Config: ${JSON.stringify(config.launchParams)}`,
        )
      }

      this.logger.log(
        `Creating pool with params: ${JSON.stringify({
          usdcAddress: poolParams.token0,
          projectTokenAddress: poolParams.token1,
          fee: poolParams.fee,
          projectManager: poolParams.launchParams.projectManager,
          whitelistedAddressForSwap: poolParams.launchParams.whitelistedAddressForSwap,
          tokenLaunchType: poolParams.launchParams.tokenLaunchType,
        })}`,
      )

      try {
        // Create and initialize the pool
        const txHash = await peripheryContract.write.createAndInitializePoolIfNecessaryWithParams([poolParams], {
          account: poolManagerClient.account!,
          chain: publicClient.chain,
        })

        // Wait for transaction confirmation
        const receipt = await publicClient.waitForTransactionReceipt({ hash: txHash })

        // Extract pool address from transaction logs
        let poolAddress: string | undefined

        // Look for pool creation event in logs
        if (receipt.logs && receipt.logs.length > 0) {
          // The pool address is typically in the first event log
          // For now, we'll get it by calling the factory again
          const newPool = await factoryContract.read.getPool([
            config.usdcAddress as Address,
            config.projectTokenAddress as Address,
            config.feeTier,
          ])
          poolAddress = newPool
        }

        this.logger.log(`Pool created successfully. TX: ${txHash}, Pool: ${poolAddress}`)

        return {
          success: true,
          chain: config.chain,
          poolAddress,
          txHash,
        }
      } catch (txError) {
        // Capture transaction data for debugging
        try {
          txDetails.data = encodeFunctionData({
            abi: peripheryAbi,
            functionName: 'createAndInitializePoolIfNecessaryWithParams',
            args: [poolParams],
          })
        } catch (encodeError) {
          this.logger.warn('Could not encode transaction data for debugging:', encodeError)
        }

        const txErrorMessage = txError instanceof Error ? txError.message : String(txError)
        const txErrorStack = txError instanceof Error ? txError.stack : undefined

        this.logger.error(
          `EVM pool creation transaction failed on ${config.chain}: ${txErrorMessage}. Details: ${JSON.stringify({
            chain: config.chain,
            errorType: txError?.constructor?.name || 'Unknown',
            txDetails: {
              to: txDetails.to,
              from: txDetails.from,
              data: txDetails.data,
            },
            poolConfig: {
              usdcAddress: config.usdcAddress,
              projectTokenAddress: config.projectTokenAddress,
              feeTier: config.feeTier,
              tgePrice: config.tgePrice,
            },
            errorStack: txErrorStack?.split('\n').slice(0, 3).join(' | '), // First 3 lines of stack
          })}`,
        )

        throw txError
      }
    } catch (error) {
      const setupErrorMessage = error instanceof Error ? error.message : String(error)
      const setupErrorStack = error instanceof Error ? error.stack : undefined

      this.logger.error(
        `EVM pool creation setup failed on ${config.chain}: ${setupErrorMessage}. Details: ${JSON.stringify({
          chain: config.chain,
          errorType: error?.constructor?.name || 'Unknown',
          poolConfig: {
            usdcAddress: config.usdcAddress,
            projectTokenAddress: config.projectTokenAddress,
            feeTier: config.feeTier,
            tgePrice: config.tgePrice,
          },
          txDetails: {
            to: txDetails?.to,
            from: txDetails?.from,
          },
          errorStack: setupErrorStack?.split('\n').slice(0, 3).join(' | '), // First 3 lines of stack
        })}`,
      )

      return {
        success: false,
        chain: config.chain,
        error: error instanceof Error ? error.message : 'Unknown error',
        debugInfo: {
          to: txDetails?.to,
          from: txDetails?.from,
          data: txDetails?.data,
        },
      }
    }
  }

  /**
   * Create pool on Solana using Raydium SDK
   */
  private async createSolanaPool(config: PoolConfig): Promise<CreatePoolResult> {
    this.logger.log(`Creating Solana pool using Raydium SDK for chain: ${config.chain}`)

    try {
      // Check if Raydium SDK is initialized
      if (!this.raydiumInstance) {
        throw new Error('Raydium SDK not initialized. Please check the service configuration.')
      }

      this.logger.log('Getting token information for pool creation...')

      // Get token information for both mints
      const [mint1Info, mint2Info] = await Promise.all([
        this.raydiumInstance.token.getTokenInfo(config.usdcAddress),
        this.raydiumInstance.token.getTokenInfo(config.projectTokenAddress),
      ])

      if (!mint1Info || !mint2Info) {
        throw new Error(
          `Token information not found for USDC (${config.usdcAddress}) or project token (${config.projectTokenAddress})`,
        )
      }

      this.logger.log('Token information retrieved:', {
        usdc: mint1Info.symbol,
        projectToken: mint2Info.symbol,
      })

      // Create Caishen-compatible AMM config instead of using standard Raydium configs
      // This config should be owned by the correct program to avoid AccountOwnedByWrongProgram error
      const ammConfig = {
        id: getPdaAmmConfigId(CLMM_PROGRAM_ID).publicKey, // Caishen AMM config
        tradeFeeRate: config.feeTier, // 0.3% fee
        protocolFeeRate: 120000, // Standard protocol fee
        tickSpacing: 60, // Standard tick spacing for 0.3% fee pools
        fundFeeRate: 40000, // Fund fee rate
        fundOwner: config.launchParams.projectManager,
        description: `CLMM Pool for ${mint2Info.symbol}/USDC`,
        index: 0,
      }

      this.logger.log('Using CLMM config:', {
        id: ammConfig.id.toString(),
        tradeFeeRate: ammConfig.tradeFeeRate,
      })

      // Create pool parameters following the Caishen SDK requirements
      const createPoolParams = {
        programId: CLMM_PROGRAM_ID,
        mint1: mint1Info,
        mint2: mint2Info,
        ammConfig,
        initialPrice: config.tgePrice, // Use number directly
        exclusiveTradingPeriodStartTime: config.launchParams.exclusiveTradingPeriodStart,
        exclusiveTradingPeriodEndTime: config.launchParams.exclusiveTradingPeriodEnd,
        projectManager: new PublicKey(config.launchParams.projectManager),
        feeTierIndex: 0,
        launchType: config.launchParams.tokenLaunchType,
        hyperlaneProgramId: HYPERLANE_PROGRAM_ID,
        vaultProgramId: VAULT_PROGRAM_ID,
        splNoopProgramId: SPL_NOOP_PROGRAM_ID,
        // Set compute budget configuration for 400k compute units
        computeBudgetConfig: {
          units: 400_000, // 400k compute units
          microLamports: 1_000_000, // 1M microLamports for priority fee (0.001 SOL)
        },
      }

      this.logger.log('Creating CLMM pool with parameters:', createPoolParams)

      // Create the pool and execute the transaction
      this.logger.log('Creating pool transaction...')
      const poolCreationResult = await this.raydiumInstance.clmm.createPool(createPoolParams)

      this.logger.log('Executing pool creation transaction on Solana with 400k compute units...')

      // Execute the transaction with wallet signing and confirmation
      const { txId } = await poolCreationResult.execute({ sendAndConfirm: true })

      // Extract pool address from the result - it's in the extInfo property
      const poolAddress = poolCreationResult.extInfo?.address?.id || 'pool-address-pending'

      this.logger.log(`🎉 Pool created successfully!`, {
        txId,
        poolAddress,
        explorerUrl: `https://explorer.solana.com/tx/${txId}`,
        poolExplorerUrl: `https://explorer.solana.com/address/${poolAddress}`,
      })

      return {
        success: true,
        chain: config.chain,
        poolAddress,
        txHash: txId,
      }
    } catch (error) {
      let errorMessage: string
      const errorDetails: any = {}

      // Handle different error types properly
      if (error instanceof Error) {
        errorMessage = error.message
        errorDetails.name = error.name
        errorDetails.stack = error.stack?.split('\n').slice(0, 5).join(' | ')

        // Extract transaction signature and get detailed logs
        const txSignatureMatch = errorMessage.match(/Transaction ([A-Za-z0-9]{43,88}) resulted in an error/)

        if (txSignatureMatch) {
          const txSignature = txSignatureMatch[1]
          errorDetails.transactionSignature = txSignature

          try {
            // Get transaction details from Solana connection
            const connection = this.blockchainService.getSolanaConnection()
            const txDetails = await connection.getTransaction(txSignature, {
              commitment: 'confirmed',
              maxSupportedTransactionVersion: 0,
            })

            if (txDetails && txDetails.meta && txDetails.meta.logMessages) {
              errorDetails.transactionLogs = txDetails.meta.logMessages
              this.logger.log('Retrieved transaction logs:', JSON.stringify(txDetails.meta.logMessages, null, 2))
            } else {
              errorDetails.transactionLogsError = 'No log messages found in transaction'
            }
          } catch (logError) {
            errorDetails.getLogsError = `Failed to retrieve transaction details: ${logError}`
          }
        }
      } else if (typeof error === 'object' && error !== null) {
        // For objects, try to extract meaningful information
        errorMessage = JSON.stringify(error, null, 2)
        errorDetails.rawError = error
      } else {
        errorMessage = String(error)
      }

      // Check if it's a transaction execution error
      const isTransactionError = errorMessage.includes('Transaction') || errorMessage.includes('simulation failed')

      this.logger.error(
        `❌ Solana pool creation failed: ${JSON.stringify(
          {
            errorMessage,
            errorDetails,
            chain: config.chain,
            errorType: error?.constructor?.name || typeof error,
            isTransactionError,
            poolConfig: {
              usdcAddress: config.usdcAddress,
              projectTokenAddress: config.projectTokenAddress,
              feeTier: config.feeTier,
              tgePrice: config.tgePrice,
              projectManager: config.launchParams.projectManager,
            },
            raydiumSdkInitialized: !!this.raydiumInstance,
          },
          null,
          2,
        )}`,
      )

      return {
        success: false,
        chain: config.chain,
        error: `Pool creation failed: ${errorMessage}`,
      }
    }
  }

  /**
   * Get pool information
   * Placeholder implementation
   */
  async getPoolInfo(poolAddress: string): Promise<any> {
    this.logger.log(`Getting pool info for address: ${poolAddress}`)

    // TODO: Implement pool info retrieval
    throw new Error('Pool info retrieval not implemented')
  }

  /**
   * Add liquidity to existing pool
   * Placeholder implementation
   */
  async addLiquidity(poolAddress: string, config: PoolConfig): Promise<CreatePoolResult> {
    this.logger.log(`Adding liquidity to pool: ${poolAddress}`)

    // TODO: Implement liquidity addition
    return {
      success: false,
      chain: config.chain,
      error: 'Add liquidity not implemented',
    }
  }
}
