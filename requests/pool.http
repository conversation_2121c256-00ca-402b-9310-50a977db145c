### Variables
@baseUrl = http://localhost:3001
@contentType = application/json
@adminKey = FORM_ADMIN_KEY_VALUE_HERE

### Test variables (replace with actual project ID from your database)
@testProjectId = f47ac10b-58cc-4372-a567-0e02b2c3d479

###############################################
### POOL CREATION (ADMIN ONLY)
### 
### All pool configuration is auto-populated from project data:
### - Blockchain from project.blockchainToLaunchOn
### - USDC address from blockchain service constants  
### - Project token from project.tokenContractAddress
### - TGE price from project.tgePriceUsdc
### - Launch dates from project.expectedTgeLaunchDate
### - Trading period from project.exclusiveTradingPeriodHours
###############################################

### Create Pool for Project (Minimal - Everything Auto-Populated)
POST {{baseUrl}}/admin/pool/create
X-Admin-Key: {{adminKey}}
Content-Type: {{contentType}}

{
  "projectId": "{{testProjectId}}"
}

### Create Pool with Optional Overrides
POST {{baseUrl}}/admin/pool/create
X-Admin-Key: {{adminKey}}
Content-Type: {{contentType}}

{
  "projectId": "{{testProjectId}}",
  "whitelistedAddressForSwap": "0x9Fbf3Fc7a1d2F4065A804CAba50Fa8F510f0340D",
  "projectManager": "0x5A22fA9238b53722486C33Ed5ab92a1AB9e86718"
}