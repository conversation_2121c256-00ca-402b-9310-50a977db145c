import { Injectable, Inject, BadRequestException, Logger } from '@nestjs/common'
import { eq } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { projects, projectStatusHistory } from '../../../libs/database/schema'

type SubmissionStatus =
  | 'draft'
  | 'submitted'
  | 'under_review'
  | 'needs_revision'
  | 'approved'
  | 'wallet_provided'
  | 'fee_paid'
  | 'liquidity_provided'

const STATUS_TRANSITIONS: Record<SubmissionStatus, SubmissionStatus[]> = {
  draft: ['submitted'],
  submitted: ['under_review', 'needs_revision'],
  under_review: ['needs_revision', 'approved'],
  needs_revision: ['submitted'],
  approved: ['wallet_provided'],
  wallet_provided: ['fee_paid'],
  fee_paid: ['liquidity_provided'],
  liquidity_provided: [], // Final state
}

@Injectable()
export class ProjectStatusService {
  private readonly logger = new Logger(ProjectStatusService.name)

  constructor(@Inject('DB') private db: NodePgDatabase<any>) {}

  async updateProjectStatus(
    projectId: string,
    newStatus: SubmissionStatus,
    changedBy: string,
    comment?: string,
  ): Promise<void> {
    // Get current project status
    const projectResult = await this.db
      .select({ submissionStatus: projects.submissionStatus })
      .from(projects)
      .where(eq(projects.projectId, projectId))
      .limit(1)

    if (projectResult.length === 0) {
      throw new BadRequestException('Project not found')
    }

    const currentStatus = projectResult[0].submissionStatus as SubmissionStatus

    // Validate status transition
    if (!this.isValidTransition(currentStatus, newStatus)) {
      throw new BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`)
    }

    // Update project status
    await this.db
      .update(projects)
      .set({
        submissionStatus: newStatus,
        updatedAt: new Date(),
      })
      .where(eq(projects.projectId, projectId))

    // Record status change in history
    await this.db.insert(projectStatusHistory).values({
      projectId,
      previousStatus: currentStatus,
      newStatus,
      changedBy,
      comment,
    })

    this.logger.log(`Project ${projectId} status changed from ${currentStatus} to ${newStatus} by ${changedBy}`)
  }

  private isValidTransition(currentStatus: SubmissionStatus, newStatus: SubmissionStatus): boolean {
    const allowedTransitions = STATUS_TRANSITIONS[currentStatus]

    return allowedTransitions.includes(newStatus)
  }

  getAvailableTransitions(currentStatus: SubmissionStatus): SubmissionStatus[] {
    return STATUS_TRANSITIONS[currentStatus] || []
  }
}
