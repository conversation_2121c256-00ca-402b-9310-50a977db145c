<context>
# Overview  
The Inclusive Layer (IL) Token API is a comprehensive backend service that powers a token launch platform for DeFi projects. It enables project creators to submit their projects, pay launch fees, and automatically create liquidity pools on multiple blockchains (Ethereum, Solana, Base, etc.). The platform streamlines the token launch process by handling everything from project submission and approval workflows to automated pool creation and fund sweeping.

# Core Features  
- **Project Management System**: Complete project lifecycle management with multi-step forms, approval workflows, and status tracking
- **Multi-Chain Support**: Native support for Ethereum, Solana, Base, Arbitrum, and other EVM chains
- **Automated Pool Creation**: Automatic liquidity pool creation on Uniswap V3 (EVM) and Raydium (Solana) after fee payment
- **Gasless Fund Sweeping**: Automated collection of launch fees using gasless transactions (permit-based for EVM, fee-payer for Solana)
- **Authentication & Authorization**: Secure email/phone OTP authentication with role-based access control
- **Token Data Management**: Comprehensive token information, market data, and rate management
- **File Upload & Storage**: Secure file handling for project documents and assets
- **Admin Dashboard**: Administrative tools for project review, status management, and system monitoring
- **Notification System**: Email and Telegram notifications for project updates and system events

# User Experience  
**Primary User Personas:**
1. **Project Creators**: DeFi teams launching new tokens who need liquidity pools
2. **Platform Administrators**: Staff who review and approve project submissions
3. **System Operators**: Technical staff managing the platform infrastructure

**Key User Flows:**
1. Project Creator Journey: Registration → Project Submission → Fee Payment → Pool Creation
2. Admin Review Flow: Project Review → Approval/Rejection → Status Management
3. Automated System Flow: Fee Detection → Pool Creation → Status Updates
</context>
<PRD>
# Technical Architecture  
**System Components:**
- **NestJS Backend**: TypeScript-based API server with modular architecture
- **PostgreSQL Database**: Primary data store with Drizzle ORM for type-safe queries
- **Multi-Chain Integration**: Viem for EVM chains, Solana Web3.js for Solana
- **Authentication**: Better-Auth for session management with OTP verification
- **File Storage**: AWS S3 integration for document and asset storage
- **Background Jobs**: Scheduled tasks for sweeping, pool creation, and data updates
- **CI/CD Pipeline**: GitHub Actions for automated testing and deployment

**Key Modules:**
- Projects: Project lifecycle management and approval workflows
- Sweep: Automated fund collection and fee processing
- Pool: Liquidity pool creation on multiple DEXs
- Auth: Authentication and user management
- Tokens: Token data and market information
- Blockchain: Multi-chain transaction handling
- Notifications: Email and messaging services

# Development Roadmap  
**Phase 1: Core Platform (MVP)**
- Project submission and management system
- Basic authentication and user management
- Single-chain pool creation (Base/Ethereum)
- Manual admin approval workflow
- Basic fund sweeping functionality

**Phase 2: Multi-Chain Expansion**
- Solana integration for pool creation
- Cross-chain fund sweeping
- Enhanced admin dashboard
- Automated approval workflows
- Advanced notification system

**Phase 3: Advanced Features**
- Real-time project analytics
- Advanced pool management tools
- Integration with external data providers
- Enhanced security features
- Performance optimizations

**Phase 4: Platform Scaling**
- API rate limiting and caching
- Advanced monitoring and alerting
- Multi-region deployment
- Enhanced admin tools
- Third-party integrations

# Logical Dependency Chain
**Foundation Layer (Must be built first):**
1. Database schema and core models
2. Authentication system
3. Basic project CRUD operations
4. File upload functionality

**Core Business Logic:**
5. Project submission workflow
6. Admin review and approval system
7. Payment processing and fee tracking
8. Basic pool creation (single chain)

**Advanced Features:**
9. Multi-chain pool creation
10. Automated fund sweeping
11. Advanced admin dashboard
12. Notification systems
13. Analytics and reporting

**Optimization & Scaling:**
14. Performance improvements
15. Enhanced security features
16. Advanced monitoring
17. Third-party integrations

# Risks and Mitigations  
**Technical Challenges:**
- Multi-chain complexity: Mitigate with robust testing and gradual rollout
- Transaction failures: Implement retry mechanisms and error handling
- Security vulnerabilities: Regular audits and security best practices

**Business Risks:**
- Regulatory compliance: Stay updated with DeFi regulations
- Market volatility: Implement dynamic fee structures
- Competition: Focus on unique value propositions and user experience

**Operational Risks:**
- System downtime: Implement redundancy and monitoring
- Data loss: Regular backups and disaster recovery plans
- Scaling issues: Design for horizontal scaling from the start

# Appendix  
**Current Implementation Status:**
- ✅ Core project management system
- ✅ Multi-chain pool creation (Base, Solana)
- ✅ Automated fund sweeping
- ✅ Authentication and authorization
- ✅ Admin dashboard functionality
- ✅ CI/CD pipeline
- 🔄 Enhanced notification system
- 🔄 Advanced analytics
- ⏳ Performance optimizations
- ⏳ Enhanced security features

**Technical Specifications:**
- Node.js 20+ with TypeScript
- NestJS framework with modular architecture
- PostgreSQL with Drizzle ORM
- Docker containerization
- AWS ECS deployment
- GitHub Actions CI/CD
</PRD>
