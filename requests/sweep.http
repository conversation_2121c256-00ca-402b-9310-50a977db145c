### Variables
@baseUrl = http://localhost:3001
@contentType = application/json

### Authentication (copy from auth.http)
@sessionToken = 22dDpQhWPUs9fgzjSxy7ykGXj4AOLxHU.zNgnut5ScWmOciUqI8CoCsrSfeWfU1lcd4%2Fe2svZNSA
@sessionData = ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

### Sweep All Pending Addresses
POST {{baseUrl}}/sweep/sweep-all
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

### Sweep Specific Address by Database ID (UUID)
# Use the database ID from the deposit-addresses response, NOT the public address
POST {{baseUrl}}/sweep/sweep/54dea786-ab94-4b34-9f35-6c22a36de4ab
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

### Sweep by Public Address (More Convenient!)
# Use the actual public address (0x... for EVM or base58 for Solana)
POST {{baseUrl}}/sweep/sweep-by-address/CtqnJCjKtGrbVCr7oQbNTTWUEHSCSX16BXmK18JKFJc2
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

### Sweep by Public Address (More Convenient!)
# Use the actual public address (0x... for EVM or base58 for Solana)
POST {{baseUrl}}/sweep/sweep-by-address/HqCpVYXfnyvToa8xLcpUr349JeTWXT7vLPtyWoeZbvhs
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

### Get Sweep Status
GET {{baseUrl}}/sweep/status
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}

###
### Testing Instructions:
### 1. First create a deposit address using deposit-addresses.http
### 2. Copy the "publicAddress" field from the response for the new endpoint
###    OR copy the "id" field (UUID format) for the original endpoint
### 3. Update the address status to 'paid'
### 4. Use either endpoint above to sweep
###
### Gasless Functionality:
### - EVM sweeps use gasless transferWithAuthorization (no ETH needed)
### - Solana sweeps use fee payer pattern (no SOL needed from user)
### - Users only need USDC in their deposit addresses
### - Service automatically pays gas/SOL fees
### - Fallback to traditional sweep if gasless fails
###

### Example: Sweep Specific Address with Real UUID
# Replace with actual UUID from deposit-addresses response
# Example UUID format: 01234567-89ab-cdef-0123-456789abcdef
POST {{baseUrl}}/sweep/sweep/01234567-89ab-cdef-0123-456789abcdef
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}}

### Manual Trigger - Use this to test the full flow
POST {{baseUrl}}/sweep/sweep-all
Cookie: __Secure-caishen-pro.session_token={{sessionToken}}; __Secure-caishen-pro.session_data={{sessionData}}
Content-Type: {{contentType}} 