import { Module } from '@nestjs/common'

import { TokensStorage } from './storage'
import { TokensController } from './tokens.controller'
import { TokensService } from './tokens.service'
import { ChainsModule } from '../chains'
import { FirebaseModule } from '../firebase'

@Module({
  exports: [TokensService],
  imports: [FirebaseModule, ChainsModule],
  providers: [TokensStorage, TokensService],
  controllers: [TokensController],
})
export class TokensModule {}
